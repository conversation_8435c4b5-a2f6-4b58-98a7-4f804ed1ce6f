import { z } from 'zod';
import { Tool } from '@/types';
import { fileOperations } from '@/operations/file-ops';
import { shellOperations } from '@/operations/shell-ops';
import { logger } from '@/utils/logger';
import { config } from '@/config';

// File operation tools
export const readFileTool: Tool = {
  name: 'read_file',
  description: 'Read the contents of a file',
  parameters: z.object({
    filePath: z.string().describe('Path to the file to read'),
  }),
  execute: async (params, context) => {
    return await fileOperations.readFile(params.filePath, context);
  },
};

export const writeFileTool: Tool = {
  name: 'write_file',
  description: 'Write content to a file',
  parameters: z.object({
    filePath: z.string().describe('Path to the file to write'),
    content: z.string().describe('Content to write to the file'),
    overwrite: z.boolean().optional().describe('Whether to overwrite existing file'),
    createDirs: z.boolean().optional().describe('Whether to create parent directories'),
  }),
  execute: async (params, context) => {
    return await fileOperations.writeFile(
      params.filePath,
      params.content,
      context,
      { overwrite: params.overwrite, createDirs: params.createDirs }
    );
  },
};

export const deleteFileTool: Tool = {
  name: 'delete_file',
  description: 'Delete a file or directory',
  parameters: z.object({
    filePath: z.string().describe('Path to the file or directory to delete'),
  }),
  execute: async (params, context) => {
    return await fileOperations.deleteFile(params.filePath, context);
  },
};

export const copyFileTool: Tool = {
  name: 'copy_file',
  description: 'Copy a file from source to destination',
  parameters: z.object({
    sourcePath: z.string().describe('Source file path'),
    destPath: z.string().describe('Destination file path'),
    overwrite: z.boolean().optional().describe('Whether to overwrite existing file'),
  }),
  execute: async (params, context) => {
    return await fileOperations.copyFile(
      params.sourcePath,
      params.destPath,
      context,
      { overwrite: params.overwrite }
    );
  },
};

export const moveFileTool: Tool = {
  name: 'move_file',
  description: 'Move a file from source to destination',
  parameters: z.object({
    sourcePath: z.string().describe('Source file path'),
    destPath: z.string().describe('Destination file path'),
  }),
  execute: async (params, context) => {
    return await fileOperations.moveFile(params.sourcePath, params.destPath, context);
  },
};

export const createDirectoryTool: Tool = {
  name: 'create_directory',
  description: 'Create a new directory',
  parameters: z.object({
    dirPath: z.string().describe('Path of the directory to create'),
  }),
  execute: async (params, context) => {
    return await fileOperations.createDirectory(params.dirPath, context);
  },
};

export const listDirectoryTool: Tool = {
  name: 'list_directory',
  description: 'List contents of a directory',
  parameters: z.object({
    dirPath: z.string().describe('Path of the directory to list'),
  }),
  execute: async (params, context) => {
    return await fileOperations.listDirectory(params.dirPath, context);
  },
};

export const searchFilesTool: Tool = {
  name: 'search_files',
  description: 'Search for files matching a pattern',
  parameters: z.object({
    pattern: z.string().describe('Glob pattern to search for files'),
    directory: z.string().optional().describe('Directory to search in (default: current)'),
    includeContent: z.boolean().optional().describe('Whether to include file content in results'),
    maxResults: z.number().optional().describe('Maximum number of results to return'),
    fileTypes: z.array(z.string()).optional().describe('File extensions to filter by'),
  }),
  execute: async (params, context) => {
    return await fileOperations.searchFiles(params.pattern, context, {
      directory: params.directory,
      includeContent: params.includeContent,
      maxResults: params.maxResults,
      fileTypes: params.fileTypes,
    });
  },
};

export const grepFilesTool: Tool = {
  name: 'grep_files',
  description: 'Search for text content within files',
  parameters: z.object({
    searchText: z.string().describe('Text to search for'),
    directory: z.string().optional().describe('Directory to search in (default: current)'),
    filePattern: z.string().optional().describe('File pattern to search within'),
    caseSensitive: z.boolean().optional().describe('Whether search is case sensitive'),
    maxResults: z.number().optional().describe('Maximum number of results to return'),
    contextLines: z.number().optional().describe('Number of context lines to include'),
  }),
  execute: async (params, context) => {
    return await fileOperations.grepFiles(params.searchText, context, {
      directory: params.directory,
      filePattern: params.filePattern,
      caseSensitive: params.caseSensitive,
      maxResults: params.maxResults,
      contextLines: params.contextLines,
    });
  },
};

export const getFileInfoTool: Tool = {
  name: 'get_file_info',
  description: 'Get detailed information about a file or directory',
  parameters: z.object({
    filePath: z.string().describe('Path to the file or directory'),
  }),
  execute: async (params, context) => {
    return await fileOperations.getFileInfo(params.filePath, context);
  },
};

export const setPermissionsTool: Tool = {
  name: 'set_permissions',
  description: 'Set file or directory permissions',
  parameters: z.object({
    filePath: z.string().describe('Path to the file or directory'),
    permissions: z.union([z.string(), z.number()]).describe('Permissions to set (e.g., "755" or 0o755)'),
  }),
  execute: async (params, context) => {
    return await fileOperations.setPermissions(params.filePath, params.permissions, context);
  },
};

// Shell operation tools
export const executeCommandTool: Tool = {
  name: 'execute_command',
  description: 'Execute a shell command',
  parameters: z.object({
    command: z.string().describe('Command to execute'),
    timeout: z.number().optional().describe('Timeout in milliseconds (default: 30000)'),
    cwd: z.string().optional().describe('Working directory for the command'),
    env: z.record(z.string()).optional().describe('Environment variables'),
    detached: z.boolean().optional().describe('Whether to run command in detached mode'),
  }),
  execute: async (params, context) => {
    return await shellOperations.executeCommand(params.command, context, {
      timeout: params.timeout,
      cwd: params.cwd,
      env: params.env,
      detached: params.detached,
    });
  },
};

export const executeScriptTool: Tool = {
  name: 'execute_script',
  description: 'Execute a script with specified interpreter',
  parameters: z.object({
    script: z.string().describe('Script content to execute'),
    interpreter: z.string().optional().describe('Script interpreter (default: bash)'),
    timeout: z.number().optional().describe('Timeout in milliseconds'),
    cwd: z.string().optional().describe('Working directory for the script'),
    env: z.record(z.string()).optional().describe('Environment variables'),
  }),
  execute: async (params, context) => {
    return await shellOperations.executeScript(params.script, context, {
      interpreter: params.interpreter,
      timeout: params.timeout,
      cwd: params.cwd,
      env: params.env,
    });
  },
};

export const killProcessTool: Tool = {
  name: 'kill_process',
  description: 'Kill a running process by PID',
  parameters: z.object({
    pid: z.number().describe('Process ID to kill'),
  }),
  execute: async (params, _context) => {
    const success = shellOperations.killProcess(params.pid);
    return {
      success,
      message: success 
        ? `Process ${params.pid} killed successfully`
        : `Failed to kill process ${params.pid} or process not found`,
      data: { pid: params.pid, killed: success },
    };
  },
};

export const listProcessesTool: Tool = {
  name: 'list_processes',
  description: 'List currently running processes started by the agent',
  parameters: z.object({}),
  execute: async (_params, _context) => {
    const processes = shellOperations.getRunningProcesses();
    return {
      success: true,
      message: `Found ${processes.length} running processes`,
      data: { processes, count: processes.length },
    };
  },
};

// Context and session tools
export const getProjectContextTool: Tool = {
  name: 'get_project_context',
  description: 'Get current project context and structure',
  parameters: z.object({
    includeFileContent: z.boolean().optional().describe('Whether to include file content in the response'),
    maxDepth: z.number().optional().describe('Maximum directory depth to include'),
  }),
  execute: async (params, context) => {
    try {
      const { includeFileContent = false, maxDepth: _maxDepth = 3 } = params;

      const projectData = {
        workingDirectory: context.workingDirectory,
        projectType: context.projectContext.type,
        structure: {
          totalFiles: context.projectContext.structure.totalFiles,
          totalDirectories: context.projectContext.structure.totalDirectories,
          lastIndexed: context.projectContext.structure.lastIndexed,
        },
        dependencies: context.projectContext.dependencies.slice(0, 20), // Limit to first 20
        configuration: context.projectContext.configuration,
        gitInfo: context.projectContext.gitInfo,
        environment: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
          cwd: process.cwd(),
        },
      };

      if (includeFileContent) {
        const limitedFiles = context.projectContext.structure.files
          .slice(0, 10)
          .map(file => ({
            path: file.path,
            name: file.name,
            size: file.size,
            extension: file.extension,
            lastModified: file.lastModified,
            content: file.content?.slice(0, 1000), // Limit content
          }));
        (projectData as any).sampleFiles = limitedFiles;
      }

      return {
        success: true,
        message: 'Project context retrieved successfully',
        data: projectData,
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Failed to retrieve project context',
        error: error.message,
      };
    }
  },
};

export const getSessionInfoTool: Tool = {
  name: 'get_session_info',
  description: 'Get current session information and statistics',
  parameters: z.object({
    includeMessages: z.boolean().optional().describe('Whether to include recent messages'),
    messageLimit: z.number().optional().describe('Number of recent messages to include'),
  }),
  execute: async (params, context) => {
    try {
      const { includeMessages = false, messageLimit = 5 } = params;

      const sessionData = {
        sessionId: context.sessionId,
        workingDirectory: context.workingDirectory,
        environmentVariables: Object.keys(context.environment).length,
        projectType: context.projectContext.type,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        timestamp: new Date().toISOString(),
      };

      if (includeMessages) {
        const { sessionManager } = await import('@/session/manager');
        const messages = sessionManager.getMessages();
        (sessionData as any).recentMessages = messages
          .slice(-messageLimit)
          .map(msg => ({
            role: msg.role,
            content: msg.content.slice(0, 200), // Truncate content
            timestamp: new Date().toISOString(),
          }));
      }

      return {
        success: true,
        message: 'Session information retrieved successfully',
        data: sessionData,
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Failed to retrieve session information',
        error: error.message,
      };
    }
  },
};

// Advanced tools for enhanced functionality
export const watchDirectoryTool: Tool = {
  name: 'watch_directory',
  description: 'Start watching a directory for file changes',
  parameters: z.object({
    directory: z.string().describe('Directory to watch'),
    patterns: z.array(z.string()).optional().describe('File patterns to watch'),
    ignorePatterns: z.array(z.string()).optional().describe('Patterns to ignore'),
  }),
  execute: async (params, _context) => {
    try {
      const { directory, patterns = ['**/*'], ignorePatterns = [] } = params;
      const chokidar = await import('chokidar');

      // Start watching the directory
      const watcher = chokidar.watch(directory, {
        ignored: ignorePatterns,
        persistent: true,
        ignoreInitial: true,
      });

      const watcherId = `watcher_${Date.now()}`;

      watcher.on('all', (event, path) => {
        logger.debug(`File ${event}: ${path}`, undefined, 'FileWatcher');
      });

      return {
        success: true,
        message: `Started watching directory: ${directory}`,
        data: {
          directory,
          patterns,
          ignorePatterns,
          watcherId,
          status: 'active',
        },
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to watch directory: ${params.directory}`,
        error: error.message,
      };
    }
  },
};

export const analyzeCodeTool: Tool = {
  name: 'analyze_code',
  description: 'Analyze code files for patterns, complexity, and issues',
  parameters: z.object({
    filePath: z.string().describe('Path to the code file to analyze'),
    analysisType: z.enum(['complexity', 'patterns', 'dependencies', 'all']).optional().describe('Type of analysis to perform'),
  }),
  execute: async (params, context) => {
    try {
      const { filePath, analysisType = 'all' } = params;
      const fileResult = await fileOperations.readFile(filePath, context);

      if (!fileResult.success) {
        return fileResult;
      }

      const content = fileResult.data?.content || '';
      const analysis: any = {
        file: filePath,
        size: content.length,
        lines: content.split('\n').length,
        timestamp: new Date().toISOString(),
      };

      if (analysisType === 'complexity' || analysisType === 'all') {
        analysis.complexity = {
          cyclomaticComplexity: calculateCyclomaticComplexity(content),
          nestingDepth: calculateNestingDepth(content),
          functionCount: (content.match(/function\s+\w+|=>\s*{|class\s+\w+/g) || []).length,
        };
      }

      if (analysisType === 'patterns' || analysisType === 'all') {
        analysis.patterns = {
          todoComments: (content.match(/\/\/\s*TODO|\/\*\s*TODO|\#\s*TODO/gi) || []).length,
          fixmeComments: (content.match(/\/\/\s*FIXME|\/\*\s*FIXME|\#\s*FIXME/gi) || []).length,
          consoleStatements: (content.match(/console\.(log|error|warn|info)/g) || []).length,
          debugStatements: (content.match(/debugger|console\.debug/g) || []).length,
        };
      }

      if (analysisType === 'dependencies' || analysisType === 'all') {
        analysis.dependencies = {
          imports: (content.match(/import\s+.*from|require\s*\(/g) || []).length,
          exports: (content.match(/export\s+|module\.exports/g) || []).length,
        };
      }

      return {
        success: true,
        message: `Code analysis completed for: ${filePath}`,
        data: analysis,
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to analyze code: ${params.filePath}`,
        error: error.message,
      };
    }
  },
};

// Helper functions for code analysis
function calculateCyclomaticComplexity(content: string): number {
  const complexityKeywords = [
    'if', 'else', 'while', 'for', 'switch', 'case', 'catch', 'try',
    '&&', '||', '?', ':', 'break', 'continue', 'return'
  ];

  let complexity = 1; // Base complexity
  for (const keyword of complexityKeywords) {
    const matches = content.match(new RegExp(`\\b${keyword}\\b`, 'g'));
    if (matches) {
      complexity += matches.length;
    }
  }

  return complexity;
}

function calculateNestingDepth(content: string): number {
  let maxDepth = 0;
  let currentDepth = 0;

  for (const char of content) {
    if (char === '{') {
      currentDepth++;
      maxDepth = Math.max(maxDepth, currentDepth);
    } else if (char === '}') {
      currentDepth--;
    }
  }

  return maxDepth;
}

export const executeParallelToolsTool: Tool = {
  name: 'execute_parallel_tools',
  description: 'Execute multiple tools in parallel for improved performance',
  parameters: z.object({
    toolCalls: z.array(z.object({
      toolName: z.string().describe('Name of the tool to execute'),
      parameters: z.record(z.any()).describe('Parameters for the tool'),
    })).describe('Array of tool calls to execute in parallel'),
    maxConcurrency: z.number().optional().describe('Maximum number of concurrent executions'),
  }),
  execute: async (params, context) => {
    try {
      const { toolCalls, maxConcurrency = 3 } = params;
      const results: any[] = [];

      // Execute tools in batches to respect concurrency limit
      for (let i = 0; i < toolCalls.length; i += maxConcurrency) {
        const batch = toolCalls.slice(i, i + maxConcurrency);
        const batchPromises = batch.map(async (toolCall: { toolName: string; parameters: Record<string, any> }) => {
          try {
            const tool = toolRegistry.getTool(toolCall.toolName);
            if (!tool) {
              return {
                toolName: toolCall.toolName,
                success: false,
                error: 'Tool not found',
              };
            }

            const result = await tool.execute(toolCall.parameters, context);
            return {
              toolName: toolCall.toolName,
              ...result,
            };
          } catch (error: any) {
            return {
              toolName: toolCall.toolName,
              success: false,
              error: error.message,
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      return {
        success: true,
        message: `Executed ${toolCalls.length} tools in parallel. ${successCount} succeeded, ${failureCount} failed.`,
        data: {
          results,
          summary: {
            total: toolCalls.length,
            successful: successCount,
            failed: failureCount,
            executionTime: Date.now(),
          },
        },
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Failed to execute parallel tools',
        error: error.message,
      };
    }
  },
};

// Network and system tools
export const networkRequestTool: Tool = {
  name: 'network_request',
  description: 'Make HTTP requests to external APIs or services',
  parameters: z.object({
    url: z.string().describe('URL to make the request to'),
    method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']).optional().describe('HTTP method'),
    headers: z.record(z.string()).optional().describe('Request headers'),
    body: z.string().optional().describe('Request body (for POST/PUT/PATCH)'),
    timeout: z.number().optional().describe('Request timeout in milliseconds'),
  }),
  execute: async (params, _context) => {
    try {
      const { url, method = 'GET', headers = {}, body, timeout = 10000 } = params;

      // Check if network access is allowed
      const cliConfig = config.getConfig();
      if (!cliConfig.tools.allowNetworkAccess) {
        return {
          success: false,
          message: 'Network access is disabled in configuration',
          error: 'NETWORK_DISABLED',
        };
      }

      const axios = await import('axios');

      const response = await axios.default({
        url,
        method,
        headers,
        data: body,
        timeout,
        validateStatus: () => true, // Don't throw on HTTP error status
      });

      return {
        success: true,
        message: `HTTP ${method} request to ${url} completed`,
        data: {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data,
          url: response.config.url,
        },
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Network request failed: ${error.message}`,
        error: error.code || 'NETWORK_ERROR',
      };
    }
  },
};

export const systemInfoTool: Tool = {
  name: 'get_system_info',
  description: 'Get detailed system information and environment details',
  parameters: z.object({
    includeEnv: z.boolean().optional().describe('Whether to include environment variables'),
    includeProcesses: z.boolean().optional().describe('Whether to include running processes info'),
  }),
  execute: async (params, _context) => {
    try {
      const { includeEnv = false, includeProcesses = false } = params;
      const os = await import('os');

      const systemInfo: any = {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        uptime: process.uptime(),
        memory: {
          total: os.totalmem(),
          free: os.freemem(),
          used: os.totalmem() - os.freemem(),
          processUsage: process.memoryUsage(),
        },
        cpu: {
          model: os.cpus()[0]?.model || 'Unknown',
          cores: os.cpus().length,
          loadAverage: os.loadavg(),
        },
        network: os.networkInterfaces(),
        hostname: os.hostname(),
        userInfo: os.userInfo(),
        tmpdir: os.tmpdir(),
        homedir: os.homedir(),
      };

      if (includeEnv) {
        systemInfo.environment = process.env;
      }

      if (includeProcesses) {
        systemInfo.process = {
          pid: process.pid,
          ppid: process.ppid,
          title: process.title,
          argv: process.argv,
          execPath: process.execPath,
          cwd: process.cwd(),
        };
      }

      return {
        success: true,
        message: 'System information retrieved successfully',
        data: systemInfo,
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Failed to retrieve system information',
        error: error.message,
      };
    }
  },
};

export const validateJsonTool: Tool = {
  name: 'validate_json',
  description: 'Validate and format JSON content',
  parameters: z.object({
    jsonContent: z.string().describe('JSON content to validate'),
    format: z.boolean().optional().describe('Whether to format the JSON'),
  }),
  execute: async (params, _context) => {
    try {
      const { jsonContent, format = false } = params;

      // Parse JSON to validate
      const parsed = JSON.parse(jsonContent);

      const result: any = {
        valid: true,
        parsed,
      };

      if (format) {
        result.formatted = JSON.stringify(parsed, null, 2);
      }

      return {
        success: true,
        message: 'JSON validation successful',
        data: result,
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'JSON validation failed',
        data: {
          valid: false,
          error: error.message,
          position: error.message.match(/position (\d+)/)?.[1],
        },
      };
    }
  },
};

// Tool registry
export const allTools: Tool[] = [
  // File operations
  readFileTool,
  writeFileTool,
  deleteFileTool,
  copyFileTool,
  moveFileTool,
  createDirectoryTool,
  listDirectoryTool,
  searchFilesTool,
  grepFilesTool,
  getFileInfoTool,
  setPermissionsTool,

  // Shell operations
  executeCommandTool,
  executeScriptTool,
  killProcessTool,
  listProcessesTool,

  // Context operations
  getProjectContextTool,
  getSessionInfoTool,

  // Advanced operations
  watchDirectoryTool,
  analyzeCodeTool,
  executeParallelToolsTool,

  // Network and system operations
  networkRequestTool,
  systemInfoTool,
  validateJsonTool,
];

export class ToolRegistry {
  private tools: Map<string, Tool> = new Map();

  constructor() {
    this.registerTools(allTools);
  }

  public registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
    logger.debug(`Tool registered: ${tool.name}`, undefined, 'ToolRegistry');
  }

  public registerTools(tools: Tool[]): void {
    tools.forEach(tool => this.registerTool(tool));
  }

  public getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  public getAllTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  public getToolNames(): string[] {
    return Array.from(this.tools.keys());
  }

  public hasTools(name: string): boolean {
    return this.tools.has(name);
  }

  public getToolsByCategory(category: string): Tool[] {
    return this.getAllTools().filter(tool => 
      tool.name.startsWith(category) || tool.description.toLowerCase().includes(category)
    );
  }
}

export const toolRegistry = new ToolRegistry();
