import fs from 'fs-extra';
import path from 'path';
import os from 'os';
import { z } from 'zod';
import { CLIConfig } from '@/types';

const ConfigSchema = z.object({
  defaultProvider: z.enum(['deepseek', 'ollama']).default('deepseek'),
  providers: z.object({
    deepseek: z.object({
      apiKey: z.string().optional(),
      baseUrl: z.string().default('https://api.deepseek.com'),
      defaultModel: z.string().default('deepseek-chat'),
    }).default({}),
    ollama: z.object({
      baseUrl: z.string().default('http://localhost:11434'),
      defaultModel: z.string().default('llama3.2'),
    }).default({}),
  }).default({}),
  session: z.object({
    autoSave: z.boolean().default(true),
    maxHistory: z.number().default(1000),
    persistContext: z.boolean().default(true),
  }).default({}),
  context: z.object({
    autoIndex: z.boolean().default(true),
    watchFiles: z.boolean().default(true),
    maxFileSize: z.number().default(1024 * 1024), // 1MB
    excludePatterns: z.array(z.string()).default([
      'node_modules/**',
      '.git/**',
      'dist/**',
      'build/**',
      '*.log',
      '.env*',
      '*.tmp',
      '*.cache',
    ]),
  }).default({}),
  tools: z.object({
    allowShellExecution: z.boolean().default(true),
    allowFileOperations: z.boolean().default(true),
    allowNetworkAccess: z.boolean().default(true),
    restrictedPaths: z.array(z.string()).default([]),
  }).default({}),
});

export class ConfigManager {
  private static instance: ConfigManager;
  private config: CLIConfig;
  private configPath: string;

  private constructor() {
    this.configPath = path.join(os.homedir(), '.agentic-cli', 'config.json');
    this.config = this.loadConfig();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private loadConfig(): CLIConfig {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readJsonSync(this.configPath);
        return ConfigSchema.parse(configData);
      }
    } catch (error) {
      console.warn('Failed to load config, using defaults:', error);
    }
    
    return this.getDefaultConfig();
  }

  private getDefaultConfig(): CLIConfig {
    return ConfigSchema.parse({});
  }

  public getConfig(): CLIConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<CLIConfig>): void {
    this.config = ConfigSchema.parse({ ...this.config, ...updates });
    this.saveConfig();
  }

  public saveConfig(): void {
    try {
      fs.ensureDirSync(path.dirname(this.configPath));
      fs.writeJsonSync(this.configPath, this.config, { spaces: 2 });
    } catch (error) {
      console.error('Failed to save config:', error);
    }
  }

  public resetConfig(): void {
    this.config = this.getDefaultConfig();
    this.saveConfig();
  }

  public getConfigPath(): string {
    return this.configPath;
  }

  public getDataDirectory(): string {
    return path.join(os.homedir(), '.agentic-cli');
  }

  public getSessionsDirectory(): string {
    return path.join(this.getDataDirectory(), 'sessions');
  }

  public getCacheDirectory(): string {
    return path.join(this.getDataDirectory(), 'cache');
  }

  public getLogsDirectory(): string {
    return path.join(this.getDataDirectory(), 'logs');
  }

  public ensureDirectories(): void {
    const directories = [
      this.getDataDirectory(),
      this.getSessionsDirectory(),
      this.getCacheDirectory(),
      this.getLogsDirectory(),
    ];

    directories.forEach(dir => {
      fs.ensureDirSync(dir);
    });
  }

  public validateProviderConfig(provider: 'deepseek' | 'ollama'): boolean {
    const config = this.config.providers[provider];

    if (provider === 'deepseek') {
      return !!(config as any).apiKey || !!process.env['DEEPSEEK_API_KEY'];
    }

    if (provider === 'ollama') {
      return !!config.baseUrl;
    }

    return false;
  }

  public getProviderConfig(provider: 'deepseek' | 'ollama') {
    const config = this.config.providers[provider];

    if (provider === 'deepseek') {
      return {
        ...config,
        apiKey: (config as any).apiKey || process.env['DEEPSEEK_API_KEY'],
      };
    }

    return config;
  }

  public setProviderApiKey(provider: 'deepseek' | 'ollama', apiKey: string): void {
    if (provider === 'deepseek') {
      this.updateConfig({
        providers: {
          ...this.config.providers,
          deepseek: {
            ...this.config.providers.deepseek,
            apiKey,
          },
        },
      });
    }
  }

  public getEnvironmentVariables(): Record<string, string> {
    return {
      AGENTIC_CLI_CONFIG_PATH: this.configPath,
      AGENTIC_CLI_DATA_DIR: this.getDataDirectory(),
      AGENTIC_CLI_SESSIONS_DIR: this.getSessionsDirectory(),
      AGENTIC_CLI_CACHE_DIR: this.getCacheDirectory(),
      AGENTIC_CLI_LOGS_DIR: this.getLogsDirectory(),
    };
  }
}

export const config = ConfigManager.getInstance();
