import { <PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ExecutionContext, AgentInstance } from '../types';
export declare class Agent implements AgentInstance {
    readonly id: string;
    readonly config: AgentConfig;
    private context;
    private isProcessing;
    constructor(agentConfig: AgentConfig, workingDirectory?: string);
    initialize(sessionId?: string): Promise<void>;
    sendMessage(message: string): Promise<string>;
    executeTools(toolCalls: ToolCall[]): Promise<ToolResult[]>;
    getContext(): ExecutionContext;
    updateWorkingDirectory(newPath: string): Promise<void>;
    refreshContext(): Promise<void>;
    getSessionId(): string;
    isReady(): boolean;
    cleanup(): Promise<void>;
}
export { Agent as AgentCore };
//# sourceMappingURL=core.d.ts.map