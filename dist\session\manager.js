"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sessionManager = exports.SessionManager = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const nanoid_1 = require("nanoid");
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
const discovery_1 = require("../context/discovery");
class SessionManager {
    static instance;
    currentSession = null;
    sessionsDir;
    constructor() {
        this.sessionsDir = config_1.config.getSessionsDirectory();
        config_1.config.ensureDirectories();
    }
    static getInstance() {
        if (!SessionManager.instance) {
            SessionManager.instance = new SessionManager();
        }
        return SessionManager.instance;
    }
    async createSession(name, workingDirectory) {
        const sessionId = (0, nanoid_1.nanoid)();
        const sessionName = name || `Session ${new Date().toLocaleString()}`;
        const workDir = workingDirectory || process.cwd();
        logger_1.logger.info(`Creating new session: ${sessionName}`, { sessionId, workDir }, 'SessionManager');
        // Discover project context
        const projectContext = await discovery_1.projectDiscovery.discoverProject(workDir);
        const fileIndex = discovery_1.projectDiscovery.createFileIndex(projectContext.structure);
        const sessionContext = {
            projectStructure: projectContext.structure,
            fileIndex,
            dependencies: projectContext.dependencies,
            environment: process.env,
            ...(projectContext.gitInfo && { gitInfo: projectContext.gitInfo }),
        };
        const session = {
            id: sessionId,
            name: sessionName,
            createdAt: new Date(),
            updatedAt: new Date(),
            workingDirectory: workDir,
            context: sessionContext,
            messages: [],
            metadata: {
                projectType: projectContext.type,
                totalFiles: projectContext.structure.totalFiles,
                totalDirectories: projectContext.structure.totalDirectories,
            },
        };
        await this.saveSession(session);
        this.currentSession = session;
        logger_1.logger.info(`Session created successfully`, {
            sessionId,
            projectType: projectContext.type,
            files: projectContext.structure.totalFiles
        }, 'SessionManager', sessionId);
        return session;
    }
    async loadSession(sessionId) {
        const sessionPath = path_1.default.join(this.sessionsDir, `${sessionId}.json`);
        if (!fs_extra_1.default.existsSync(sessionPath)) {
            throw new Error(`Session not found: ${sessionId}`);
        }
        try {
            const sessionData = await fs_extra_1.default.readJson(sessionPath);
            const session = this.deserializeSession(sessionData);
            // Refresh project context if needed
            if (this.shouldRefreshContext(session)) {
                await this.refreshSessionContext(session);
            }
            this.currentSession = session;
            logger_1.logger.info(`Session loaded successfully`, {
                sessionId,
                messageCount: session.messages.length
            }, 'SessionManager', sessionId);
            return session;
        }
        catch (error) {
            logger_1.logger.error(`Failed to load session: ${sessionId}`, error, 'SessionManager');
            throw new Error(`Failed to load session: ${error}`);
        }
    }
    async saveSession(session) {
        try {
            session.updatedAt = new Date();
            const sessionPath = path_1.default.join(this.sessionsDir, `${session.id}.json`);
            const serializedSession = this.serializeSession(session);
            await fs_extra_1.default.writeJson(sessionPath, serializedSession, { spaces: 2 });
            logger_1.logger.debug(`Session saved`, { sessionId: session.id }, 'SessionManager', session.id);
        }
        catch (error) {
            logger_1.logger.error(`Failed to save session: ${session.id}`, error, 'SessionManager', session.id);
            throw new Error(`Failed to save session: ${error}`);
        }
    }
    async deleteSession(sessionId) {
        const sessionPath = path_1.default.join(this.sessionsDir, `${sessionId}.json`);
        if (fs_extra_1.default.existsSync(sessionPath)) {
            await fs_extra_1.default.remove(sessionPath);
            if (this.currentSession?.id === sessionId) {
                this.currentSession = null;
            }
            logger_1.logger.info(`Session deleted`, { sessionId }, 'SessionManager');
        }
    }
    async listSessions() {
        try {
            const files = await fs_extra_1.default.readdir(this.sessionsDir);
            const sessionFiles = files.filter(file => file.endsWith('.json'));
            const sessions = [];
            for (const file of sessionFiles) {
                try {
                    const sessionPath = path_1.default.join(this.sessionsDir, file);
                    const sessionData = await fs_extra_1.default.readJson(sessionPath);
                    const session = this.deserializeSession(sessionData);
                    sessions.push(session);
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to load session file: ${file}`, error, 'SessionManager');
                }
            }
            // Sort by updated date (most recent first)
            sessions.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
            return sessions;
        }
        catch (error) {
            logger_1.logger.error('Failed to list sessions', error, 'SessionManager');
            return [];
        }
    }
    getCurrentSession() {
        return this.currentSession;
    }
    async addMessage(message) {
        if (!this.currentSession) {
            throw new Error('No active session');
        }
        this.currentSession.messages.push(message);
        // Limit message history
        const maxHistory = config_1.config.getConfig().session.maxHistory;
        if (this.currentSession.messages.length > maxHistory) {
            this.currentSession.messages = this.currentSession.messages.slice(-maxHistory);
        }
        if (config_1.config.getConfig().session.autoSave) {
            await this.saveSession(this.currentSession);
        }
        logger_1.logger.debug(`Message added to session`, {
            role: message.role,
            sessionId: this.currentSession.id
        }, 'SessionManager', this.currentSession.id);
    }
    getMessages() {
        return this.currentSession?.messages || [];
    }
    async refreshSessionContext(session) {
        try {
            logger_1.logger.info(`Refreshing session context`, { sessionId: session.id }, 'SessionManager', session.id);
            const projectContext = await discovery_1.projectDiscovery.refreshProject(session.workingDirectory);
            const fileIndex = discovery_1.projectDiscovery.createFileIndex(projectContext.structure);
            session.context = {
                projectStructure: projectContext.structure,
                fileIndex,
                dependencies: projectContext.dependencies,
                environment: process.env,
                ...(projectContext.gitInfo && { gitInfo: projectContext.gitInfo }),
            };
            session.metadata = {
                ...session.metadata,
                projectType: projectContext.type,
                totalFiles: projectContext.structure.totalFiles,
                totalDirectories: projectContext.structure.totalDirectories,
            };
            await this.saveSession(session);
            logger_1.logger.info(`Session context refreshed`, {
                sessionId: session.id,
                files: projectContext.structure.totalFiles
            }, 'SessionManager', session.id);
        }
        catch (error) {
            logger_1.logger.error(`Failed to refresh session context`, error, 'SessionManager', session.id);
            throw error;
        }
    }
    shouldRefreshContext(session) {
        const maxAge = 10 * 60 * 1000; // 10 minutes
        const age = Date.now() - session.context.projectStructure.lastIndexed.getTime();
        return age > maxAge;
    }
    serializeSession(session) {
        return {
            ...session,
            createdAt: session.createdAt.toISOString(),
            updatedAt: session.updatedAt.toISOString(),
            context: {
                ...session.context,
                projectStructure: {
                    ...session.context.projectStructure,
                    lastIndexed: session.context.projectStructure.lastIndexed.toISOString(),
                },
                fileIndex: {
                    files: Array.from(session.context.fileIndex.files.entries()),
                    directories: Array.from(session.context.fileIndex.directories.entries()),
                    byExtension: Array.from(session.context.fileIndex.byExtension.entries()),
                    bySize: Array.from(session.context.fileIndex.bySize.entries()),
                    searchIndex: Array.from(session.context.fileIndex.searchIndex.entries()),
                },
            },
        };
    }
    deserializeSession(data) {
        return {
            ...data,
            createdAt: new Date(data.createdAt),
            updatedAt: new Date(data.updatedAt),
            context: {
                ...data.context,
                projectStructure: {
                    ...data.context.projectStructure,
                    lastIndexed: new Date(data.context.projectStructure.lastIndexed),
                },
                fileIndex: {
                    files: new Map(data.context.fileIndex.files),
                    directories: new Map(data.context.fileIndex.directories),
                    byExtension: new Map(data.context.fileIndex.byExtension),
                    bySize: new Map(data.context.fileIndex.bySize),
                    searchIndex: new Map(data.context.fileIndex.searchIndex),
                },
            },
        };
    }
    async exportSession(sessionId, outputPath) {
        const session = await this.loadSession(sessionId);
        const exportData = {
            session: this.serializeSession(session),
            exportedAt: new Date().toISOString(),
            version: '1.0.0',
        };
        await fs_extra_1.default.writeJson(outputPath, exportData, { spaces: 2 });
        logger_1.logger.info(`Session exported`, { sessionId, outputPath }, 'SessionManager', sessionId);
    }
    async importSession(importPath) {
        const importData = await fs_extra_1.default.readJson(importPath);
        const session = this.deserializeSession(importData.session);
        // Generate new ID to avoid conflicts
        session.id = (0, nanoid_1.nanoid)();
        session.name = `${session.name} (Imported)`;
        await this.saveSession(session);
        logger_1.logger.info(`Session imported`, { sessionId: session.id }, 'SessionManager', session.id);
        return session;
    }
    async cleanupOldSessions(maxAge = 30 * 24 * 60 * 60 * 1000) {
        const sessions = await this.listSessions();
        const cutoffDate = new Date(Date.now() - maxAge);
        let deletedCount = 0;
        for (const session of sessions) {
            if (session.updatedAt < cutoffDate) {
                await this.deleteSession(session.id);
                deletedCount++;
            }
        }
        logger_1.logger.info(`Cleaned up ${deletedCount} old sessions`, undefined, 'SessionManager');
    }
}
exports.SessionManager = SessionManager;
exports.sessionManager = SessionManager.getInstance();
//# sourceMappingURL=manager.js.map