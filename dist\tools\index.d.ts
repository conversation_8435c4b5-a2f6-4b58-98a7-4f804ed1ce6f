import { Tool } from '../types';
export declare const readFileTool: Tool;
export declare const writeFileTool: Tool;
export declare const deleteFileTool: Tool;
export declare const copyFileTool: Tool;
export declare const moveFileTool: Tool;
export declare const createDirectoryTool: Tool;
export declare const listDirectoryTool: Tool;
export declare const searchFilesTool: Tool;
export declare const grepFilesTool: Tool;
export declare const getFileInfoTool: Tool;
export declare const setPermissionsTool: Tool;
export declare const executeCommandTool: Tool;
export declare const executeScriptTool: Tool;
export declare const killProcessTool: Tool;
export declare const listProcessesTool: Tool;
export declare const getProjectContextTool: Tool;
export declare const getSessionInfoTool: Tool;
export declare const allTools: Tool[];
export declare class ToolRegistry {
    private tools;
    constructor();
    registerTool(tool: Tool): void;
    registerTools(tools: Tool[]): void;
    getTool(name: string): Tool | undefined;
    getAllTools(): Tool[];
    getToolNames(): string[];
    hasTools(name: string): boolean;
    getToolsByCategory(category: string): Tool[];
}
export declare const toolRegistry: ToolRegistry;
//# sourceMappingURL=index.d.ts.map